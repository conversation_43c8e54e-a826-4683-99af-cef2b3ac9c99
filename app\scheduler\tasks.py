"""
Background tasks for intelligent data collection.
PATTERN: APScheduler with Flask for automated data collection.
CRITICAL: Implements smart scheduling to avoid API rate limits.
"""

import logging
from datetime import datetime, timedelta, timezone, date
from typing import List, Dict, Any

from flask import current_app
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger

from app import db, scheduler
from app.api.deye_client import DeyeAPIClient, DeyeAPIError
from app.services.deye_service import DeyeStationService
from app.models.station import Station
from app.models.device import Device
from app.models.measurement import StationMeasurement, DeviceMeasurement

logger = logging.getLogger(__name__)


def collect_live_data():
    """
    Collect live data from all active stations.
    CRITICAL: Runs every 5 minutes for real-time monitoring.
    PATTERN: Store all data in database for future processing.
    """
    logger.info("Starting live data collection task")
    
    try:
        service = DeyeStationService()
        
        # Get all active stations
        stations = Station.query.filter_by(is_active=True).all()
        
        if not stations:
            logger.warning("No active stations found for live data collection")
            return
        
        logger.info(f"Collecting live data for {len(stations)} stations")
        
        for station in stations:
            try:
                # Get real-time station data using the service
                api_data = service.fetch_station_latest_data(station)
                
                if api_data:
                    # Store the data to database using the service
                    solar_data = service.store_latest_data_to_db(station, api_data)
                    
                    if solar_data:
                        # Update station's last data update timestamp
                        station.last_data_update = datetime.utcnow()
                        logger.debug(f"Collected live data for station {station.name}")
                    else:
                        logger.warning(f"Failed to store live data for station {station.name}")
                else:
                    logger.warning(f"No live data returned for station {station.name}")
                    
            except Exception as e:
                logger.error(f"Unexpected error collecting live data for station {station.name}: {e}")
                continue
        
        # Commit all updates at once
        db.session.commit()
        logger.info("Live data collection completed successfully")
        
    except Exception as e:
        logger.error(f"Critical error in live data collection: {e}")
        db.session.rollback()


def collect_daily_data():
    """
    Collect daily historical data for all active stations.
    CRITICAL: Runs once per day at 23:59 for previous day's data.
    PATTERN: Intelligent scheduling to avoid API abuse.
    """
    logger.info("Starting daily data collection task")
    
    try:
        with DeyeAPIClient() as client:
            # Get all active stations
            stations = Station.query.filter_by(is_active=True).all()
            
            if not stations:
                logger.warning("No active stations found for daily data collection")
                return
            
            # Get yesterday's date in yyyy-MM-dd format
            yesterday = date.today() - timedelta(days=1)
            date_str = yesterday.strftime("%Y-%m-%d")
            
            logger.info(f"Collecting daily data for {len(stations)} stations for date {date_str}")
            
            for station in stations:
                try:
                    # Get daily historical data (granularity=2)
                    api_data_list = client.get_station_history_data(
                        int(station.deye_station_id), 
                        granularity=2, 
                        start_date=date_str,
                        end_date=date_str
                    )
                    
                    if api_data_list:
                        for api_data in api_data_list:
                            # Check if we already have this data
                            existing = StationMeasurement.query.filter_by(
                                station_id=station.id,
                                granularity='daily',
                                timestamp=yesterday
                            ).first()
                            
                            if not existing:
                                # Create measurement from API data
                                measurement = StationMeasurement.create_from_api_data(
                                    api_data, station.id, granularity='daily'
                                )
                                measurement.timestamp = datetime.combine(yesterday, datetime.min.time())
                                
                                db.session.add(measurement)
                                logger.debug(f"Collected daily data for station {station.name}")
                            else:
                                logger.debug(f"Daily data already exists for station {station.name} on {date_str}")
                    else:
                        logger.warning(f"No daily data returned for station {station.name}")
                        
                except DeyeAPIError as e:
                    logger.error(f"API error collecting daily data for station {station.name}: {e}")
                    continue
                except Exception as e:
                    logger.error(f"Unexpected error collecting daily data for station {station.name}: {e}")
                    continue
            
            # Commit all measurements at once
            db.session.commit()
            logger.info("Daily data collection completed successfully")
            
    except Exception as e:
        logger.error(f"Critical error in daily data collection: {e}")
        db.session.rollback()


def collect_monthly_data():
    """
    Collect monthly historical data for all active stations.
    CRITICAL: Runs once per month for previous month's data.
    PATTERN: Long-term historical data storage.
    """
    logger.info("Starting monthly data collection task")
    
    try:
        with DeyeAPIClient() as client:
            # Get all active stations
            stations = Station.query.filter_by(is_active=True).all()
            
            if not stations:
                logger.warning("No active stations found for monthly data collection")
                return
            
            # Get last month's date in yyyy-MM format
            today = date.today()
            if today.month == 1:
                last_month = date(today.year - 1, 12, 1)
            else:
                last_month = date(today.year, today.month - 1, 1)
            
            date_str = last_month.strftime("%Y-%m")
            
            logger.info(f"Collecting monthly data for {len(stations)} stations for month {date_str}")
            
            for station in stations:
                try:
                    # Get monthly historical data (granularity=3)
                    api_data_list = client.get_station_history_data(
                        int(station.deye_station_id),
                        granularity=3,
                        start_date=date_str,
                        end_date=date_str
                    )
                    
                    if api_data_list:
                        for api_data in api_data_list:
                            # Check if we already have this data
                            existing = StationMeasurement.query.filter_by(
                                station_id=station.id,
                                granularity='monthly',
                                timestamp=last_month
                            ).first()
                            
                            if not existing:
                                # Create measurement from API data
                                measurement = StationMeasurement.create_from_api_data(
                                    api_data, station.id, granularity='monthly'
                                )
                                measurement.timestamp = datetime.combine(last_month, datetime.min.time())
                                
                                db.session.add(measurement)
                                logger.debug(f"Collected monthly data for station {station.name}")
                            else:
                                logger.debug(f"Monthly data already exists for station {station.name} for {date_str}")
                    else:
                        logger.warning(f"No monthly data returned for station {station.name}")
                        
                except DeyeAPIError as e:
                    logger.error(f"API error collecting monthly data for station {station.name}: {e}")
                    continue
                except Exception as e:
                    logger.error(f"Unexpected error collecting monthly data for station {station.name}: {e}")
                    continue
            
            # Commit all measurements at once
            db.session.commit()
            logger.info("Monthly data collection completed successfully")
            
    except Exception as e:
        logger.error(f"Critical error in monthly data collection: {e}")
        db.session.rollback()


def sync_stations_and_devices():
    """
    Synchronize stations and devices from Deye API.
    CRITICAL: Ensures database has current station and device information.
    """
    logger.info("Starting station and device synchronization")
    
    try:
        with DeyeAPIClient() as client:
            # Get station list from API
            api_stations = client.get_station_list()
            
            logger.info(f"Found {len(api_stations)} stations in Deye API")
            
            for api_station in api_stations:
                station_id = str(api_station.get('id'))
                
                # Check if station exists in database
                station = Station.query.filter_by(deye_station_id=station_id).first()
                
                if not station:
                    # Create new station
                    station = Station.create_from_api_data(api_station)
                    db.session.add(station)
                    logger.info(f"Created new station: {station.name}")
                else:
                    # Update existing station
                    station.name = api_station.get('name', station.name)
                    station.location = api_station.get('address', station.location)
                    station.capacity = api_station.get('capacity', station.capacity)
                    station.updated_at = datetime.utcnow()
                    logger.debug(f"Updated station: {station.name}")
            
            db.session.commit()
            logger.info("Station and device synchronization completed successfully")
            
    except Exception as e:
        logger.error(f"Error in station and device synchronization: {e}")
        db.session.rollback()


def manual_data_refresh():
    """
    Manual data refresh triggered by user action.
    CRITICAL: Immediate data collection for dashboard refresh.
    """
    logger.info("Manual data refresh triggered")
    
    try:
        # Collect live data immediately
        collect_live_data()
        
        # Also sync stations and devices
        sync_stations_and_devices()
        
        logger.info("Manual data refresh completed")
        return True
        
    except Exception as e:
        logger.error(f"Error in manual data refresh: {e}")
        return False


def start_scheduled_tasks():
    """
    Initialize all background tasks with proper scheduling.
    PATTERN: APScheduler configuration based on research/apscheduler/page1.md
    """
    try:
        logger.info("Starting scheduled tasks configuration")
        
        # CRITICAL: Live data collection every 5 minutes
        scheduler.add_job(
            func=collect_live_data,
            trigger=IntervalTrigger(minutes=current_app.config.get('LIVE_DATA_INTERVAL', 5)),
            id='collect_live_data',
            name='Collect Live Data',
            replace_existing=True,
            max_instances=1,  # Prevent overlapping executions
            coalesce=True     # If multiple instances are queued, run only the latest
        )
        
        # CRITICAL: Daily data collection once per day at 23:59
        scheduler.add_job(
            func=collect_daily_data,
            trigger=CronTrigger(
                hour=current_app.config.get('DAILY_DATA_HOUR', 23),
                minute=current_app.config.get('DAILY_DATA_MINUTE', 59)
            ),
            id='collect_daily_data',
            name='Collect Daily Data',
            replace_existing=True,
            max_instances=1
        )
        
        # CRITICAL: Monthly data collection on 1st day of month at 00:30
        scheduler.add_job(
            func=collect_monthly_data,
            trigger=CronTrigger(day=1, hour=0, minute=30),
            id='collect_monthly_data',
            name='Collect Monthly Data',
            replace_existing=True,
            max_instances=1
        )
        
        # Station/device sync every 6 hours
        scheduler.add_job(
            func=sync_stations_and_devices,
            trigger=IntervalTrigger(hours=6),
            id='sync_stations_devices',
            name='Sync Stations and Devices',
            replace_existing=True,
            max_instances=1
        )
        
        logger.info("All scheduled tasks configured successfully")
        
        # Log the scheduled jobs
        jobs = scheduler.get_jobs()
        logger.info(f"Configured {len(jobs)} scheduled jobs:")
        for job in jobs:
            logger.info(f"  - {job.name} (ID: {job.id}) - Next run: {job.next_run_time}")
        
    except Exception as e:
        logger.error(f"Error configuring scheduled tasks: {e}")
        raise

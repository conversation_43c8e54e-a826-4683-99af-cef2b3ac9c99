{% extends "base.html" %}

{% block title %}Solar Dashboard{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Station selector -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-solar-panel text-warning me-2"></i>
                                Solar Dashboard
                            </h5>
                            <p class="card-text text-muted mb-0">Real-time monitoring and analytics</p>
                        </div>
                        <div class="col-md-6 text-end">
                            {% if stations %}
                            <select id="station-selector" class="form-select" style="max-width: 300px; display: inline-block;">
                                {% for station in stations %}
                                <option value="{{ station.id }}" {% if loop.first %}selected{% endif %}>
                                    {{ station.name }}
                                </option>
                                {% endfor %}
                            </select>
                            {% else %}
                            <div class="alert alert-warning mb-0" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                No stations configured. Please check your Deye Cloud API connection.
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% if stations %}
    <!-- Live data cards -->
    <div class="row mb-4" id="live-cards-container">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card live-card" id="pv-generation-card">
                <div class="card-body text-center">
                    <div class="live-card-icon">
                        <i class="fas fa-sun text-warning"></i>
                    </div>
                    <h6 class="card-title">PV Generation</h6>
                    <div class="live-value" id="pv-generation-value">
                        <span class="value">0.0</span>
                        <span class="unit">kW</span>
                    </div>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-warning" id="pv-generation-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted" id="pv-generation-status">--</small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card live-card" id="battery-level-card">
                <div class="card-body text-center">
                    <div class="live-card-icon">
                        <i class="fas fa-battery-three-quarters text-success"></i>
                    </div>
                    <h6 class="card-title">Battery Level</h6>
                    <div class="live-value" id="battery-soc-value">
                        <span class="value">0</span>
                        <span class="unit">%</span>
                    </div>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-success" id="battery-soc-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted" id="battery-status">--</small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card live-card" id="grid-power-card">
                <div class="card-body text-center">
                    <div class="live-card-icon">
                        <i class="fas fa-plug text-info"></i>
                    </div>
                    <h6 class="card-title">Grid Import</h6>
                    <div class="live-value" id="grid-power-value">
                        <span class="value">0.0</span>
                        <span class="unit">kW</span>
                    </div>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-info" id="grid-power-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted" id="grid-status">--</small>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card live-card" id="consumption-card">
                <div class="card-body text-center">
                    <div class="live-card-icon">
                        <i class="fas fa-home text-primary"></i>
                    </div>
                    <h6 class="card-title">Consumption</h6>
                    <div class="live-value" id="consumption-value">
                        <span class="value">0.0</span>
                        <span class="unit">kW</span>
                    </div>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-primary" id="consumption-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted" id="consumption-status">--</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                Power Data
                            </h5>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-controls text-end">
                                <div class="btn-group me-3" role="group">
                                    <input type="radio" class="btn-check" name="chart-view" id="daily-view" value="live" checked>
                                    <label class="btn btn-outline-primary btn-sm" for="daily-view">Today</label>
                                    
                                    <input type="radio" class="btn-check" name="chart-view" id="weekly-view" value="daily">
                                    <label class="btn btn-outline-primary btn-sm" for="weekly-view">Week</label>
                                    
                                    <input type="radio" class="btn-check" name="chart-view" id="monthly-view" value="monthly">
                                    <label class="btn btn-outline-primary btn-sm" for="monthly-view">Month</label>
                                </div>
                                
                                <input type="date" id="date-picker" class="form-control form-control-sm" style="display: inline-block; width: auto;">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="power-chart" width="400" height="150"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary statistics -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        Today's Summary
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="summary-stat">
                                <div class="stat-value" id="total-generation">0.0 kWh</div>
                                <div class="stat-label">Total Generation</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="summary-stat">
                                <div class="stat-value" id="total-consumption">0.0 kWh</div>
                                <div class="stat-label">Total Consumption</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="summary-stat">
                                <div class="stat-value" id="grid-import">0.0 kWh</div>
                                <div class="stat-label">Grid Import</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="summary-stat">
                                <div class="stat-value" id="system-efficiency">0%</div>
                                <div class="stat-label">System Efficiency</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Loading overlay -->
<div class="loading-overlay" id="loading-overlay" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; justify-content: center; align-items: center;">
    <div class="loading-spinner text-center">
        <div class="spinner-border text-warning" role="status" style="width: 3rem; height: 3rem;">
            <span class="visually-hidden">Loading...</span>
        </div>
        <div class="loading-text mt-3 text-white h5">Loading...</div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', function() {
    {% if stations %}
    // Initialize dashboard
    try {
        window.dashboard = new SolarDashboard();
    } catch (error) {
        console.error('Dashboard initialization failed:', error);
        document.body.insertAdjacentHTML('beforeend', 
            '<div class="alert alert-danger position-fixed" style="top: 20px; right: 20px; z-index: 10000;">' +
            'Failed to initialize dashboard. Please refresh the page.</div>');
    }
    {% else %}
    console.log('No stations configured');
    {% endif %}
});
</script>
{% endblock %}

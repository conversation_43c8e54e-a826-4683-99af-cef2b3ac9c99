/**
 * Main dashboard JavaScript
 * PATTERN: Modern ES6+ with error handling and accessibility
 */

class SolarDashboard {
    constructor() {
        this.currentStationId = null;
        this.charts = {};
        this.refreshInterval = null;
        this.isRefreshing = false;
        
        // Configuration
        this.config = {
            refreshIntervalMs: 30000, // 30 seconds
            chartUpdateDelay: 500,
            animationDuration: 300,
            maxRetries: 3,
            retryDelay: 1000
        };
        
        this.init();
    }
    
    /**
     * Initialize the dashboard
     */
    async init() {
        try {
            this.showLoading('Initializing dashboard...');
            // Initialize components
            this.setupEventListeners();
            this.setupConnectionMonitoring();
            // Load stations (auto-selects and loads data for first station)
            await this.loadStations();
            // Start auto-refresh
            this.startAutoRefresh();
            this.hideLoading();
            // Show success message
            this.showNotification('Dashboard initialized successfully', 'success');
        } catch (error) {
            console.error('Dashboard initialization failed:', error);
            this.hideLoading();
            this.showError('Failed to initialize dashboard. Please refresh the page.');
        }
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Manual refresh button
        const refreshBtn = document.getElementById('manual-refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.manualRefresh();
            });
        }
        
        // Station selection
        document.addEventListener('click', (e) => {
            if (e.target.closest('.station-card')) {
                const stationCard = e.target.closest('.station-card');
                const stationId = stationCard.dataset.stationId;
                if (stationId) {
                    this.selectStation(stationId);
                }
            }
        });
        
        // Chart controls
        document.addEventListener('change', (e) => {
            if (e.target.matches('.chart-period-selector')) {
                this.updateChartPeriod(e.target.value);
            }
            if (e.target.matches('.chart-date-selector')) {
                this.updateChartDate(e.target.value);
            }
        });
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                e.preventDefault();
                this.manualRefresh();
            }
        });
        
        // Window events
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
        
        window.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseAutoRefresh();
            } else {
                this.resumeAutoRefresh();
            }
        });
    }
    
    /**
     * Setup connection monitoring
     */
    setupConnectionMonitoring() {
        const statusIndicator = document.getElementById('connection-status');
        
        // Check connection periodically
        setInterval(() => {
            this.checkConnection().then(isOnline => {
                if (statusIndicator) {
                    statusIndicator.className = isOnline ? 
                        'badge bg-success' : 'badge bg-danger';
                    statusIndicator.textContent = isOnline ? 'Online' : 'Offline';
                }
            });
        }, 5000);
    }
    
    /**
     * Load available stations
     */
    async loadStations() {
        try {
            const response = await this.apiCall('/api/stations');
            const stations = response.data;
            
            if (stations && stations.length > 0) {
                this.renderStations(stations);
                // Auto-select first station
                await this.selectStation(stations[0].id);
            } else {
                this.showNoStationsMessage();
            }
            
        } catch (error) {
            console.error('Failed to load stations:', error);
            this.showError('Failed to load solar stations. Please check your connection.');
        }
    }
    

    
    /**
     * Render stations list
     */
    renderStations(stations) {
        const container = document.getElementById('stations-container');
        if (!container) return;
        
        container.innerHTML = stations.map(station => `
            <div class="col-md-6 col-lg-4">
                <div class="card station-card" data-station-id="${station.id}">
                    <div class="card-body">
                        <h5 class="station-name">${this.escapeHtml(station.name)}</h5>
                        <p class="station-info">
                            ${station.device_count || 0} devices<br>
                            <small class="text-muted">ID: ${station.id}</small>
                        </p>
                    </div>
                </div>
            </div>
        `).join('');
        
        // Add animations
        container.querySelectorAll('.station-card').forEach((card, index) => {
            card.style.animationDelay = `${index * 100}ms`;
            card.classList.add('slide-in-up');
        });
    }
    
    /**
     * Select a station and load its data
     */
    async selectStation(stationId) {
        try {
            // Update UI selection
            document.querySelectorAll('.station-card').forEach(card => {
                card.classList.remove('active');
            });
            
            const selectedCard = document.querySelector(`[data-station-id="${stationId}"]`);
            if (selectedCard) {
                selectedCard.classList.add('active');
            }
            
            this.currentStationId = stationId;
            
            // Load station data
            await this.loadStationData(stationId);
            await this.loadChartData(stationId);
            
        } catch (error) {
            console.error('Failed to select station:', error);
            this.showError('Failed to load station data.');
        }
    }
    
    /**
     * Load live data for a station
     */
    async loadStationData(stationId) {
        try {
            const response = await this.apiCall(`/api/stations/${stationId}/latest`);
            const responseData = response.data;
            
            if (responseData && responseData.data) {
                this.updateLiveCards(responseData.data);
                this.updateDataFreshness(responseData.data.timestamp);
            }
            
        } catch (error) {
            console.error('Failed to load station data:', error);
            this.showError('Failed to load live data.');
        }
    }
    
    /**
     * Update live cards with new data
     */
    updateLiveCards(data) {
        const updates = [
            { id: 'pv-generation-card', value: (data.generationPower || 0) / 1000, unit: 'kW' },
            { id: 'battery-level-card', value: data.batterySOC || 0, unit: '%' },
            { id: 'grid-power-card', value: (data.gridImport || 0) / 1000, unit: 'kW' },
            { id: 'consumption-card', value: (data.consumptionPower || 0) / 1000, unit: 'kW' }
        ];
        
        updates.forEach(update => {
            const card = document.getElementById(update.id);
            if (card) {
                this.animateCardUpdate(card, update.value, update.unit);
            }
        });
    }
    
    /**
     * Animate card value update
     */
    animateCardUpdate(card, newValue, unit) {
        const valueElement = card.querySelector('.value');
        const cardElement = card.closest('.live-card');
        
        if (!valueElement) return;
        
        // Add updating animation
        cardElement.classList.add('updating');
        
        // Simple update without complex animation
        setTimeout(() => {
            if (unit === '%') {
                valueElement.textContent = Math.round(newValue).toString();
            } else {
                valueElement.textContent = newValue.toFixed(1);
            }
            cardElement.classList.remove('updating');
            
            // Add positive glow effect for positive values
            if (newValue > 0) {
                cardElement.classList.add('positive');
            } else {
                cardElement.classList.remove('positive');
            }
        }, 100);
    }
    
    /**
     * Load chart data
     */
    async loadChartData(stationId, period = 'today') {
        try {
            this.showChartLoading();
            
            const response = await this.apiCall(`/api/stations/${stationId}/history?period=${period}`);
            const data = response.data;
            
            if (data && data.length > 0) {
                await this.updateCharts(data);
            } else {
                this.showNoChartData();
            }
            
        } catch (error) {
            console.error('Failed to load chart data:', error);
            this.showChartError('Failed to load chart data.');
        } finally {
            this.hideChartLoading();
        }
    }
    
    /**
     * Manual refresh
     */
    async manualRefresh() {
        if (this.isRefreshing) return;
        
        this.isRefreshing = true;
        const refreshBtn = document.getElementById('manual-refresh-btn');
        
        try {
            if (refreshBtn) {
                refreshBtn.classList.add('loading');
                refreshBtn.disabled = true;
            }
            
            if (this.currentStationId) {
                await this.loadStationData(this.currentStationId);
                await this.loadChartData(this.currentStationId);
            }
            
            this.showNotification('Data refreshed successfully', 'success');
            
        } catch (error) {
            console.error('Manual refresh failed:', error);
            this.showNotification('Refresh failed. Please try again.', 'error');
        } finally {
            this.isRefreshing = false;
            if (refreshBtn) {
                refreshBtn.classList.remove('loading');
                refreshBtn.disabled = false;
            }
        }
    }
    
    /**
     * Start auto-refresh
     */
    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            if (!this.isRefreshing && this.currentStationId) {
                this.loadStationData(this.currentStationId);
            }
        }, this.config.refreshIntervalMs);
    }
    
    /**
     * Pause auto-refresh
     */
    pauseAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
    
    /**
     * Resume auto-refresh
     */
    resumeAutoRefresh() {
        if (!this.refreshInterval) {
            this.startAutoRefresh();
        }
    }
    
    /**
     * API call helper with retry logic
     */
    async apiCall(endpoint, options = {}, retryCount = 0) {
        try {
            const response = await fetch(endpoint, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
            
        } catch (error) {
            if (retryCount < this.config.maxRetries) {
                await this.delay(this.config.retryDelay * (retryCount + 1));
                return this.apiCall(endpoint, options, retryCount + 1);
            }
            throw error;
        }
    }
    
    /**
     * Check connection status
     */
    async checkConnection() {
        try {
            const response = await fetch('/api/health', { 
                method: 'HEAD',
                cache: 'no-cache'
            });
            return response.ok;
        } catch {
            return false;
        }
    }
    
    /**
     * Utility functions
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    /**
     * UI state management
     */
    showLoading(message = 'Loading...') {
        const overlay = document.querySelector('.loading-overlay');
        if (overlay) {
            overlay.querySelector('.loading-text').textContent = message;
            overlay.style.display = 'flex';
        }
    }
    
    hideLoading() {
        const overlay = document.querySelector('.loading-overlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }
    
    showChartLoading() {
        document.querySelectorAll('.chart-loading').forEach(el => {
            el.style.display = 'block';
        });
    }
    
    hideChartLoading() {
        document.querySelectorAll('.chart-loading').forEach(el => {
            el.style.display = 'none';
        });
    }
    
    showChartError(message) {
        document.querySelectorAll('.chart-error').forEach(el => {
            el.textContent = message;
            el.style.display = 'block';
        });
    }
    
    showNoChartData() {
        document.querySelectorAll('.no-data-message').forEach(el => {
            el.style.display = 'block';
        });
    }
    
    showNoStationsMessage() {
        const container = document.getElementById('stations-container');
        if (container) {
            container.innerHTML = `
                <div class="col-12">
                    <div class="no-data-message">
                        <i class="fas fa-solar-panel"></i>
                        <h4>No Solar Stations Found</h4>
                        <p>No solar stations are currently available. Please check your Deye Cloud configuration.</p>
                    </div>
                </div>
            `;
        }
    }
    
    showError(message) {
        this.showNotification(message, 'error');
    }
    
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 10000; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
    
    updateDataFreshness(timestamp) {
        const elements = document.querySelectorAll('.data-freshness');
        if (!timestamp) return;
        
        const now = new Date();
        const dataTime = new Date(timestamp);
        const diffMinutes = (now - dataTime) / (1000 * 60);
        
        let className = 'fresh';
        let text = 'Just now';
        
        if (diffMinutes > 60) {
            className = 'old';
            text = `${Math.floor(diffMinutes / 60)}h ago`;
        } else if (diffMinutes > 10) {
            className = 'stale';
            text = `${Math.floor(diffMinutes)}min ago`;
        } else if (diffMinutes > 1) {
            text = `${Math.floor(diffMinutes)}min ago`;
        }
        
        elements.forEach(el => {
            el.className = `data-freshness ${className}`;
            el.textContent = `Updated ${text}`;
        });
    }
    
    /**
     * Cleanup
     */
    cleanup() {
        this.pauseAutoRefresh();
        
        // Destroy charts
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
        
        this.charts = {};
    }
}

// Initialize dashboard when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.solarDashboard = new SolarDashboard();
});

// Handle page unload
window.addEventListener('beforeunload', () => {
    if (window.solarDashboard) {
        window.solarDashboard.cleanup();
    }
});
